/**
 * @name OrderSender Final
 * @description Финальная рабочая версия плагина для отправки сообщений на сервер
 * @version 1.0.0
 * <AUTHOR>
 * @website https://tgapi-pink.vercel.app
 * @source https://raw.githubusercontent.com/yourusername/yourrepo/main/betterdiscord-plugin/OrderSender-Final.plugin.js
 */

const config = {
    main: "index.js",
    name: "OrderSender Final",
    author: "YourName",
    version: "1.0.0",
    description: "Финальная рабочая версия плагина для отправки сообщений на сервер",
    github: "https://github.com/yourusername/yourrepo",
    github_raw: "https://raw.githubusercontent.com/yourusername/yourrepo/main/betterdiscord-plugin/OrderSender-Final.plugin.js",
    changelog: [],
    defaultConfig: [
        {
            type: "textbox",
            id: "apiUrl",
            name: "URL API",
            note: "URL для отправки сообщений",
            value: "https://tgapi-pink.vercel.app/api/send-to-sheet"
        },
        {
            type: "switch",
            id: "debugMode",
            name: "Режим отладки",
            note: "Показывать отладочную информацию в консоли",
            value: false
        }
    ]
};

class Dummy {
    constructor() {this._config = config;}
    start() {}
    stop() {}
}
 
if (!global.ZeresPluginLibrary) {
    BdApi.showConfirmationModal("Library Missing", `The library plugin needed for ${config.name} is missing. Please click Download Now to install it.`, {
        confirmText: "Download Now",
        cancelText: "Cancel",
        onConfirm: () => {
            require("request").get("https://betterdiscord.app/gh-redirect?id=9", async (err, resp, body) => {
                if (err) return require("electron").shell.openExternal("https://betterdiscord.app/Download?id=9");
                if (resp.statusCode === 302) {
                    require("request").get(resp.headers.location, async (error, response, content) => {
                        if (error) return require("electron").shell.openExternal("https://betterdiscord.app/Download?id=9");
                        await new Promise(r => require("fs").writeFile(require("path").join(BdApi.Plugins.folder, "0PluginLibrary.plugin.js"), content, r));
                    });
                }
                else {
                    await new Promise(r => require("fs").writeFile(require("path").join(BdApi.Plugins.folder, "0PluginLibrary.plugin.js"), body, r));
                }
            });
        }
    });
}
 
module.exports = !global.ZeresPluginLibrary ? Dummy : (([Plugin, Api]) => {
    const plugin = (Plugin, Library) => {
        const { Logger, Settings } = Library;
        const { ContextMenu } = window.BdApi;

        return class OrderSenderFinal extends Plugin {
            constructor() {
                super();
                this.settings = this.loadSettings();
            }

            onStart() {
                try {
                    this.patchContextMenu();
                    Logger.info("OrderSender Final плагин запущен, контекстное меню пропатчено!");
                    window.BdApi.showToast("✅ OrderSender запущен", { type: "success" });
                } catch (err) {
                    Logger.error("Ошибка при патчинге контекстного меню:", err);
                    window.BdApi.showToast("❌ Ошибка запуска OrderSender", { type: "error" });
                }
            }

            onStop() {
                this.contextMenuPatch?.();
                Logger.info("OrderSender Final плагин остановлен!");
                window.BdApi.showToast("⏹️ OrderSender остановлен", { type: "info" });
            }

            patchContextMenu() {
                const callback = (tree, props) => {
                    if (!props.message) return;

                    try {
                        // Используем третий метод - добавляем в tree.props.children.props.children
                        if (tree.props.children && tree.props.children.props && tree.props.children.props.children && Array.isArray(tree.props.children.props.children)) {
                            tree.props.children.props.children.push(
                                ContextMenu.buildItem({
                                    id: "send-to-server",
                                    label: "📤 Отправить на сервер",
                                    action: () => {
                                        this.sendMessageToAPI(props.message);
                                    },
                                })
                            );

                            if (this.settings.debugMode) {
                                console.log("[OrderSender Final] Элемент меню добавлен успешно (Method 3)");
                            }
                        }
                    } catch (error) {
                        console.error("[OrderSender Final] Ошибка добавления элемента в меню:", error);
                    }
                };

                this.contextMenuPatch = ContextMenu.patch("message", callback);
            }

            // Функция для извлечения полного текста сообщения
            extractFullMessageText(message) {
                let fullText = "";

                // Основной текст сообщения
                if (message.content) {
                    fullText += message.content;
                }

                // Текст из embeds (встроенных элементов)
                if (message.embeds && message.embeds.length > 0) {
                    message.embeds.forEach((embed) => {
                        if (fullText) fullText += "\n\n";

                        // Заголовок embed
                        if (embed.title) {
                            fullText += `**${embed.title}**\n`;
                        }

                        // Описание embed
                        if (embed.description) {
                            fullText += embed.description;
                        }

                        // Поля embed
                        if (embed.fields && embed.fields.length > 0) {
                            embed.fields.forEach(field => {
                                if (fullText && !fullText.endsWith('\n')) fullText += "\n";
                                fullText += `**${field.name}**: ${field.value}`;
                            });
                        }

                        // URL embed
                        if (embed.url) {
                            if (fullText && !fullText.endsWith('\n')) fullText += "\n";
                            fullText += `URL: ${embed.url}`;
                        }
                    });
                }

                // Информация о вложениях
                if (message.attachments && message.attachments.length > 0) {
                    message.attachments.forEach(attachment => {
                        if (fullText) fullText += "\n";
                        fullText += `📎 Вложение: ${attachment.filename}`;
                        if (attachment.description) {
                            fullText += ` - ${attachment.description}`;
                        }
                    });
                }

                return fullText.trim();
            }

            async sendMessageToAPI(message) {
                try {
                    // Получаем данные через window.BdApi
                    const ChannelStore = window.BdApi.Webpack.getStore("ChannelStore");
                    const UserStore = window.BdApi.Webpack.getStore("UserStore");

                    const channel = ChannelStore?.getChannel(message.channel_id);
                    const user = UserStore?.getCurrentUser();

                    // Извлекаем полный текст сообщения
                    const fullMessageText = this.extractFullMessageText(message);

                    if (!channel || !user || !fullMessageText) {
                        if (this.settings.debugMode) {
                            Logger.warn("Недостаточно данных для отправки сообщения");
                            Logger.warn("Структура сообщения:", {
                                content: message.content,
                                embeds: message.embeds,
                                attachments: message.attachments,
                                extractedText: fullMessageText
                            });
                        }
                        window.BdApi.showToast("❌ Недостаточно данных для отправки", { type: "error" });
                        return;
                    }

                    const messageData = {
                        text: fullMessageText,
                        chat: channel.name || channel.id,
                        currentUserName: user.username || user.globalName || "Unknown",
                        userId: user.id,
                        platform: 'discord'
                    };

                    if (this.settings.debugMode) {
                        Logger.info("Отправляем сообщение на API:", {
                            originalContent: message.content || "(пусто)",
                            hasEmbeds: message.embeds ? message.embeds.length : 0,
                            hasAttachments: message.attachments ? message.attachments.length : 0,
                            extractedText: messageData.text.substring(0, 200) + (messageData.text.length > 200 ? "..." : ""),
                            fullTextLength: messageData.text.length,
                            chat: messageData.chat,
                            currentUserName: messageData.currentUserName,
                            userId: messageData.userId
                        });
                    }

                    // Показываем уведомление о начале отправки
                    window.BdApi.showToast("📤 Отправляем сообщение...", { type: "info" });

                    const response = await fetch(this.settings.apiUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(messageData)
                    });

                    if (response.ok) {
                        window.BdApi.showToast("✅ Сообщение отправлено на сервер", { type: "success" });
                        if (this.settings.debugMode) {
                            Logger.info("Сообщение успешно отправлено на API");
                        }
                    } else {
                        const errorText = await response.text();
                        window.BdApi.showToast(`❌ Ошибка API: ${response.status}`, { type: "error" });
                        if (this.settings.debugMode) {
                            Logger.warn(`Ошибка API: ${response.status} ${response.statusText}`, errorText);
                        }
                    }
                } catch (error) {
                    window.BdApi.showToast("❌ Ошибка сети", { type: "error" });
                    if (this.settings.debugMode) {
                        Logger.error("Ошибка отправки на API:", error);
                    }
                }
            }

            getSettingsPanel() {
                return Settings.SettingPanel.build(this.saveSettings.bind(this), 
                    new Settings.Textbox("URL API", "URL для отправки сообщений", this.settings.apiUrl, (value) => {
                        this.settings.apiUrl = value;
                    }),
                    new Settings.Switch("Режим отладки", "Показывать отладочную информацию в консоли", this.settings.debugMode, (value) => {
                        this.settings.debugMode = value;
                    })
                );
            }

            saveSettings() {
                window.BdApi.Data.save(config.name, "settings", this.settings);
            }

            loadSettings() {
                const defaultSettings = {};
                config.defaultConfig.forEach(setting => {
                    defaultSettings[setting.id] = setting.value;
                });
                return Object.assign(defaultSettings, window.BdApi.Data.load(config.name, "settings") || {});
            }
        };
    };
    return plugin(Plugin, Api);
})(global.ZeresPluginLibrary.buildPlugin(config));
