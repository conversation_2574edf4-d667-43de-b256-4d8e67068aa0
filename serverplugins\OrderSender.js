/**
 * @name OrderSender
 * @version 1.0.0
 * @description Плагин для отправки сообщений на сервер через контекстное меню (серверная версия)
 * <AUTHOR>
 * @source https://github.com/YourRepo/OrderSender
 * @website https://tgapi-pink.vercel.app
 */

class OrderSender {
    constructor() {
        this.contextMenuPatch = null;
        this.settings = {
            apiUrl: "https://tgapi-pink.vercel.app/api/send-to-sheet",
            debugMode: false
        };
        this.isStarted = false;
    }

    // Метод, вызываемый при запуске плагина
    start() {
        console.log("OrderSender: Запуск плагина...");
        try {
            this.loadSettings();
            this.patchContextMenu();
            this.isStarted = true;
            console.log("OrderSender: Плагин успешно запущен!");
            this.showToast("✅ OrderSender запущен", "success");
        } catch (error) {
            console.error("OrderSender: Ошибка при запуске плагина:", error);
            this.showToast("❌ Ошибка запуска OrderSender", "error");
        }
    }

    // Метод, вызываемый при остановке плагина
    stop() {
        console.log("OrderSender: Остановка плагина...");
        try {
            if (this.contextMenuPatch) {
                this.contextMenuPatch();
                this.contextMenuPatch = null;
            }
            this.isStarted = false;
            console.log("OrderSender: Плагин остановлен!");
            this.showToast("⏹️ OrderSender остановлен", "info");
        } catch (error) {
            console.error("OrderSender: Ошибка при остановке плагина:", error);
        }
    }

    // Загрузка настроек
    loadSettings() {
        try {
            const savedSettings = BdApi.Data.load("OrderSender", "settings");
            if (savedSettings) {
                this.settings = { ...this.settings, ...savedSettings };
                if (this.settings.debugMode) {
                    console.log("OrderSender: Настройки загружены:", this.settings);
                }
            }
        } catch (error) {
            console.error("OrderSender: Ошибка загрузки настроек:", error);
        }
    }

    // Сохранение настроек
    saveSettings() {
        try {
            BdApi.Data.save("OrderSender", "settings", this.settings);
            if (this.settings.debugMode) {
                console.log("OrderSender: Настройки сохранены:", this.settings);
            }
        } catch (error) {
            console.error("OrderSender: Ошибка сохранения настроек:", error);
        }
    }

    // Патчинг контекстного меню
    patchContextMenu() {
        if (!BdApi.ContextMenu) {
            console.error("OrderSender: BdApi.ContextMenu недоступен");
            return;
        }

        const callback = (tree, props) => {
            if (!props.message) return;

            try {
                // Ищем место для вставки пункта меню
                if (tree.props.children && tree.props.children.props && tree.props.children.props.children && Array.isArray(tree.props.children.props.children)) {
                    tree.props.children.props.children.push(
                        BdApi.ContextMenu.buildItem({
                            id: "send-to-server",
                            label: "📤 Отправить на сервер",
                            action: () => {
                                this.sendMessageToAPI(props.message);
                            },
                        })
                    );

                    if (this.settings.debugMode) {
                        console.log("OrderSender: Элемент меню добавлен успешно");
                    }
                }
            } catch (error) {
                console.error("OrderSender: Ошибка добавления элемента в меню:", error);
            }
        };

        this.contextMenuPatch = BdApi.ContextMenu.patch("message", callback);
        
        if (this.settings.debugMode) {
            console.log("OrderSender: Контекстное меню пропатчено");
        }
    }

    // Отправка сообщения на API
    async sendMessageToAPI(message) {
        try {
            // Получаем данные через BdApi
            const ChannelStore = BdApi.Webpack.getStore("ChannelStore");
            const UserStore = BdApi.Webpack.getStore("UserStore");
            
            const channel = ChannelStore?.getChannel(message.channel_id);
            const user = UserStore?.getCurrentUser();
            
            if (!channel || !user || !message.content) {
                if (this.settings.debugMode) {
                    console.warn("OrderSender: Недостаточно данных для отправки сообщения");
                }
                this.showToast("❌ Недостаточно данных для отправки", "error");
                return;
            }

            const messageData = {
                text: message.content,
                chat: channel.name || channel.id,
                currentUserName: user.username || user.globalName || "Unknown",
                userId: user.id,
                platform: 'discord'
            };

            if (this.settings.debugMode) {
                console.log("OrderSender: Отправляем сообщение на API:", {
                    text: messageData.text.substring(0, 100) + (messageData.text.length > 100 ? "..." : ""),
                    chat: messageData.chat,
                    currentUserName: messageData.currentUserName,
                    userId: messageData.userId
                });
            }

            // Показываем уведомление о начале отправки
            this.showToast("📤 Отправляем сообщение...", "info");

            const response = await fetch(this.settings.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(messageData)
            });

            if (response.ok) {
                this.showToast("✅ Сообщение отправлено на сервер", "success");
                if (this.settings.debugMode) {
                    console.log("OrderSender: Сообщение успешно отправлено на API");
                }
            } else {
                const errorText = await response.text();
                this.showToast(`❌ Ошибка API: ${response.status}`, "error");
                if (this.settings.debugMode) {
                    console.warn(`OrderSender: Ошибка API: ${response.status} ${response.statusText}`, errorText);
                }
            }
        } catch (error) {
            this.showToast("❌ Ошибка сети", "error");
            if (this.settings.debugMode) {
                console.error("OrderSender: Ошибка отправки на API:", error);
            }
        }
    }

    // Вспомогательный метод для показа уведомлений
    showToast(message, type) {
        try {
            if (BdApi.showToast) {
                BdApi.showToast(message, { type: type });
            } else {
                console.log(`OrderSender Toast [${type}]: ${message}`);
            }
        } catch (error) {
            console.log(`OrderSender Toast [${type}]: ${message}`);
        }
    }

    // Метод для получения панели настроек (если потребуется)
    getSettingsPanel() {
        // Создаем простую HTML панель настроек
        const panel = document.createElement("div");
        panel.style.cssText = "padding: 20px; color: var(--text-normal); font-family: var(--font-primary);";

        // Заголовок
        const title = document.createElement("h2");
        title.textContent = "OrderSender - Настройки";
        title.style.cssText = "margin-bottom: 20px; color: var(--header-primary);";
        panel.appendChild(title);

        // URL API
        const urlLabel = document.createElement("label");
        urlLabel.textContent = "URL API:";
        urlLabel.style.cssText = "display: block; margin-bottom: 5px; font-weight: 500;";
        
        const urlInput = document.createElement("input");
        urlInput.type = "text";
        urlInput.value = this.settings.apiUrl;
        urlInput.style.cssText = "width: 100%; padding: 8px; margin-bottom: 15px; background: var(--background-secondary); color: var(--text-normal); border: 1px solid var(--background-tertiary); border-radius: 4px;";
        
        // Режим отладки
        const debugLabel = document.createElement("label");
        debugLabel.style.cssText = "display: flex; align-items: center; margin-bottom: 20px; cursor: pointer;";
        
        const debugCheckbox = document.createElement("input");
        debugCheckbox.type = "checkbox";
        debugCheckbox.checked = this.settings.debugMode;
        debugCheckbox.style.cssText = "margin-right: 8px;";
        
        const debugText = document.createElement("span");
        debugText.textContent = "Режим отладки";
        
        debugLabel.appendChild(debugCheckbox);
        debugLabel.appendChild(debugText);

        // Кнопка сохранения
        const saveButton = document.createElement("button");
        saveButton.textContent = "Сохранить настройки";
        saveButton.style.cssText = "padding: 10px 20px; background: var(--brand-experiment); color: white; border: none; border-radius: 4px; cursor: pointer;";
        
        saveButton.onclick = () => {
            this.settings.apiUrl = urlInput.value;
            this.settings.debugMode = debugCheckbox.checked;
            this.saveSettings();
            this.showToast("Настройки сохранены!", "success");
        };

        panel.appendChild(urlLabel);
        panel.appendChild(urlInput);
        panel.appendChild(debugLabel);
        panel.appendChild(saveButton);

        return panel;
    }
}
