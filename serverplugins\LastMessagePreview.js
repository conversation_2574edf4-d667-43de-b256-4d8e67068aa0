/**
 * @name LastMessagePreview
 * <AUTHOR>
 * @description Показывает превью последнего сообщения под названием чата в списке личных сообщений
 * @version 0.1.0
 * @website https://github.com/yourusername/LastMessagePreview
 * @source https://github.com/yourusername/LastMessagePreview
 */

class LastMessagePreview {
    constructor() {
        this.initialized = false;
        this.defaultSettings = {
            maxPreviewLength: 50,
            showImages: true,
            showEmojis: true,
            previewOpacity: 0.7,
            showTimestamp: false,
            debug: true,
        };
        this.settings = Object.assign({}, this.defaultSettings);
        this.observers = [];
        this.updateIntervalId = null;
        this.dmChannelsCache = new Map(); // Кэш для хранения последних сообщений
        this._isUpdating = false;
        this._lastUpdate = 0;
        this.updateDelay = 1000; // Минимальная задержка между обновлениями
        this.dispatcher = null; // Добавляем поле для диспетчера
        this.handleMessageCreate = this.handleMessageCreate.bind(this); // Привязываем контекст обработчика
        
        // CSS стили
        this.css = `
            .last-message-preview {
                color: var(--text-muted);
                font-size: 12px;
                margin-top: 2px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                max-width: 100%;
                opacity: ${this.settings.previewOpacity};
            }
            
            .last-message-preview-icon {
                margin-right: 4px;
                display: inline-block;
            }
            
            .last-message-preview:hover {
                color: var(--text-normal);
                transition: color 0.2s ease;
            }
        `;
        
        // Селекторы для DOM элементов - обновлены для большей гибкости
        this.selectors = {
            // Список личных сообщений (разные варианты)
            dmList: [
                "[aria-label='Direct Messages']", 
                "[class*='privateChannels-']",
                "div[class*='sidebar'] nav[class*='container'] div[class*='scroller']",
                "nav[aria-label*='Servers'] + div",
                "nav + div" // Самый общий селектор
            ],
            // Элементы списка личных сообщений
            dmItems: [
                "[class*='channel-'][class*='interactive-'][role='listitem']",
                "div[class*='channel'][role='listitem']",
                "div[class*='interactive-'][role='listitem']",
                "div[role='listitem']" // Самый общий селектор
            ],
            // Имя пользователя в ЛС
            nameWrapper: [
                "[class*='nameAndDecorators-']",
                "div[class*='name']",
                "div > div > div:nth-child(2)" // Пробуем позиционное расположение
            ]
        };
    }

    // Метод для логирования
    log(message, type = 'log') {
        // Выводим error/warn всегда, info/log только если debug: true
        if (!this.settings.debug && type !== 'error' && type !== 'warn') return;
        
        const types = {
            log: 'color: #00ff00',
            warn: 'color: #ffff00', 
            error: 'color: #ff0000', 
            info: 'color: #00aaff' 
        };
        // Используем info как стандартный цвет, если debug включен
        const defaultColor = this.settings.debug ? types.info : types.log;
        console.log(`%c[LastMessagePreview] ${message}`, types[type] || defaultColor);
    }

    getName() { return "LastMessagePreview"; }
    getDescription() { return "Показывает превью последнего сообщения под названием чата в списке личных сообщений"; }
    getVersion() { return "0.1.0"; }
    getAuthor() { return "YourName"; }

    // Метод для получения панели настроек
    getSettingsPanel() {
        this.log("Открытие панели настроек");
        const { Settings } = BdApi;
        const settingsPanel = document.createElement("div");
        
        new Settings.SettingPanel(
            () => this.saveSettings(),
            new Settings.Slider("Максимальная длина превью", 
                "Максимальное количество символов для отображения в превью", 
                10, 100, this.settings.maxPreviewLength, 
                (value) => { this.settings.maxPreviewLength = value; }, 
                { markers: [10, 25, 50, 75, 100], stickToMarkers: false }
            ),
            new Settings.Switch("Показывать изображения", 
                "Отображать иконку изображения, если последнее сообщение содержит картинку", 
                this.settings.showImages, 
                (value) => { this.settings.showImages = value; }
            ),
            new Settings.Switch("Показывать эмодзи", 
                "Отображать эмодзи в превью сообщений", 
                this.settings.showEmojis, 
                (value) => { this.settings.showEmojis = value; }
            ),
            new Settings.Switch("Показывать время сообщения", 
                "Отображать время отправки последнего сообщения", 
                this.settings.showTimestamp, 
                (value) => { this.settings.showTimestamp = value; }
            ),
            new Settings.Slider("Прозрачность превью", 
                "Уровень прозрачности текста превью", 
                0.1, 1, this.settings.previewOpacity, 
                (value) => { 
                    this.settings.previewOpacity = value;
                    this.updateCSSVariables();
                }, 
                { markers: [0.1, 0.3, 0.5, 0.7, 1], stickToMarkers: false }
            ),
            new Settings.Button("Сбросить настройки", "Вернуть все настройки к значениям по умолчанию", 
                () => {
                    this.settings = Object.assign({}, this.defaultSettings);
                    this.saveSettings();
                    this.updateAllPreviews();
                    BdApi.showToast("Настройки сброшены", {type: "success"});
                }
            ),
            new Settings.Switch("Режим отладки", 
                "Включить подробное логирование в консоль для отладки", 
                this.settings.debug, 
                (value) => { 
                    this.settings.debug = value;
                    this.log(`Режим отладки ${value ? 'включен' : 'выключен'}`);
                }
            )
        ).appendTo(settingsPanel);
        
        return settingsPanel;
    }

    // Сохранение настроек
    saveSettings() {
        this.log("Сохранение настроек");
        BdApi.saveData(this.getName(), "settings", this.settings);
        this.updateCSSVariables();
        this.updateAllPreviews();
    }

    // Загрузка настроек
    loadSettings() {
        this.log("Загрузка настроек");
        const loadedSettings = BdApi.loadData(this.getName(), "settings");
        this.settings = Object.assign({}, this.defaultSettings, loadedSettings);
        this.log("Текущие настройки:", "info");
        this.log(JSON.stringify(this.settings, null, 2));
    }

    start() {
        this.log("Запуск плагина", "info");
        this.loadSettings();
        this.initialize();
        this.findDispatcher(); // Находим диспетчер
        this.subscribeToEvents(); // Подписываемся на события
    }

    stop() {
        this.log("Остановка плагина", "info");
        this.removeStyles();
        this.disconnectObservers();
        this.unsubscribeFromEvents(); // Отписываемся от событий
        // Дополнительно очищаем все превью при остановке
        document.querySelectorAll('.last-message-preview').forEach(el => el.remove());
        this.dmChannelsCache.clear();
    }

    initialize() {
        this.log("Инициализация плагина", "info");
        try {
            this.addStyles();
            // Убираем немедленный вызов updateAllPreviews отсюда
            this.setupObservers(); // Наблюдатель нужен только для обнаружения *появления* списка ЛС
        } catch (error) {
            this.log(`Ошибка инициализации: ${error.message}`, "error");
        }
    }

    shutdown() {
        if (!this.initialized) {
            this.log("Плагин не был инициализирован", "warn");
            return;
        }
        this.log("Отключение плагина");
        this.initialized = false;
        
        try {
            this.removeStyles();
            this.log("Стили удалены успешно");
        } catch (e) {
            this.log(`Ошибка при удалении стилей: ${e.message}`, "error");
        }
        
        try {
            this.disconnectObservers();
            this.log("Наблюдатели отключены успешно");
        } catch (e) {
            this.log(`Ошибка при отключении наблюдателей: ${e.message}`, "error");
        }
        
        // Удаляем все превью
        this.removeAllPreviews();
    }
    
    // Удаление всех превью сообщений
    removeAllPreviews() {
        document.querySelectorAll(".last-message-preview").forEach(el => {
            el.remove();
        });
    }
    
    // Настройка наблюдателей за изменениями в DOM
    setupObservers() {
        this.log("Настройка наблюдателей DOM", "info");
        
        // --- Наблюдатель за появлением списка ЛС --- 
        const appRoot = document.querySelector("#app-mount");
        if (appRoot && !document.querySelector(this.selectors.dmList)) { // Проверяем, что списка еще нет
            const rootObserver = new MutationObserver((mutations) => {
                // Ищем появление списка ЛС
                let dmListAppeared = false;
                for (const mutation of mutations) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                         for(const node of mutation.addedNodes) {
                             if (node.nodeType === Node.ELEMENT_NODE && node.matches(this.selectors.dmList)) {
                                 dmListAppeared = true;
                                 break;
                             }
                             if (node.nodeType === Node.ELEMENT_NODE && node.querySelector(this.selectors.dmList)) {
                                 dmListAppeared = true;
                                 break;
                             }
                         }
                    }
                    if (dmListAppeared) break;
                }
                
                if (dmListAppeared) {
                    this.log("Список ЛС появился в DOM, выполняем начальную загрузку превью.", "info");
                    this.updateAllPreviews(); // Выполняем начальную загрузку
                    // Отключаем этого наблюдателя, он больше не нужен
                    rootObserver.disconnect();
                    this.observers = this.observers.filter(obs => obs !== rootObserver);
                    this.log("Наблюдатель за появлением списка ЛС отключен.", "info");
                }
            });
            
            rootObserver.observe(appRoot, { childList: true, subtree: true });
            this.observers.push(rootObserver);
            this.log("Наблюдатель за появлением списка ЛС настроен.", "info");
        } else if(document.querySelector(this.selectors.dmList)) {
             // Если список ЛС уже есть при запуске setupObservers
             this.log("Список ЛС уже существует, выполняем начальную загрузку превью.", "info");
             this.updateAllPreviews(); // Выполняем начальную загрузку
        } else {
             this.log("#app-mount не найден, наблюдатель за появлением списка ЛС не настроен.", "warn");
        }
        
        // --- Удаляем наблюдателя за изменениями *внутри* списка ЛС --- 
        // const dmList = document.querySelector(this.selectors.dmList);
        // if (dmList) { ... }
    }
    
    // Отключение всех наблюдателей
    disconnectObservers() {
        this.log(`Отключение ${this.observers.length} наблюдателей`);
        this.observers.forEach(observer => observer.disconnect());
        this.observers = [];
    }

    // Обновление CSS переменных
    updateCSSVariables() {
        this.log("Обновление CSS переменных");
        document.querySelectorAll(".last-message-preview").forEach(el => {
            el.style.opacity = this.settings.previewOpacity;
        });
    }

    // Обновление всех превью сообщений (теперь только для начальной загрузки)
    updateAllPreviews() {
         // Убираем проверку на частые вызовы, т.к. вызывается редко
         // const now = Date.now();
         // if (now - this._lastUpdate < this.updateDelay) { ... }
         // this._lastUpdate = now;
        
        this.log("Начальная загрузка всех превью сообщений", "info");
        
        const listItems = document.querySelectorAll("li[role='listitem']");
        if (listItems.length > 0) {
            // this.log(`Найдено ${listItems.length} элементов li[role="listitem"]`);
            const dmItems = Array.from(listItems).filter(item => {
                const link = item.querySelector("a[href*='/channels/@me/']");
                return link !== null;
            });
            
            if (dmItems.length > 0) {
                // this.log(`После фильтрации осталось ${dmItems.length} элементов ЛС`);
                this.processItems(dmItems); // Используем тот же метод для начальной загрузки
                return;
            }
        }
        
        // Резервный поиск (если li не найдены)
        let dmItemsFallback = [];
        for (const selector of this.selectors.dmItems) {
            const items = document.querySelectorAll(selector);
            if (items.length > 0) {
                dmItemsFallback = Array.from(items);
                break;
            }
        }
        if (dmItemsFallback.length > 0) {
            this.processItems(dmItemsFallback);
        } else {
             this.log("Не найдено элементов ЛС для начальной загрузки превью.", "warn");
        }
    }
    
    // Выделяем обработку элементов чата в отдельный метод
    processItems(dmItems) {
        this.log(`Начальная обработка ${dmItems.length} элементов ЛС`, "info"); 
        
        dmItems.forEach((item, index) => {
            try {
                // Все еще убраны подробные логи для каждого элемента
                let element = item;
                if (item.tagName === 'LI') {
                    const link = item.querySelector("a[href*='/channels/@me/']");
                    if (link) element = link;
                }
                
                const channelId = this.getChannelIdFromElement(element);
                if (!channelId) return;
                
                let nameWrapper = null;
                let parentElement = element;
                if (element.tagName === 'A') {
                    parentElement = element.closest('li') || element.closest('div[role="listitem"]') || element.parentElement;
                }
                
                for (const selector of this.selectors.nameWrapper) {
                    const wrapper = parentElement.querySelector(selector);
                    if (wrapper) {
                        nameWrapper = wrapper;
                        break;
                    }
                }
                
                if (!nameWrapper) {
                    const divs = parentElement.querySelectorAll("div");
                    for (let i = 0; i < divs.length; i++) {
                        const div = divs[i];
                        if (div.textContent && div.children.length === 0) {
                            nameWrapper = div.parentElement;
                            break;
                        }
                    }
                    if (!nameWrapper && divs.length >= 2) nameWrapper = divs[1];
                }
                
                if (!nameWrapper) nameWrapper = parentElement;
                
                const existingPreview = parentElement.querySelector(".last-message-preview");
                if (existingPreview) existingPreview.remove();
                
                this.getLastMessage(channelId).then(message => {
                    // Теперь message - это объект сообщения или null
                    if (!message) return; 
                    
                    // Убираем проверку на Array.isArray и id === "error-message"
                    // т.к. getLastMessage теперь возвращает чистое сообщение или null

                    const previewElement = this.createPreviewElement(message);
                    if (previewElement) {
                        nameWrapper.appendChild(previewElement);
                    }
                });
            } catch (error) {
                this.log(`Ошибка processItems для элемента ${index}: ${error.message}`, "error");
            }
        });
    }
    
    // Получение ID канала из элемента
    getChannelIdFromElement(element) {
        // Проверяем атрибуты элемента
        const href = element.getAttribute("href");
        if (href && href.startsWith("/channels/@me/")) {
            return href.split("/").pop();
        }
        
        // Проверяем data-list-item-id
        const listItemId = element.getAttribute("data-list-item-id");
        if (listItemId) {
            const match = listItemId.match(/private-channels-\d+-(\d+)/);
            if (match && match[1]) {
                return match[1];
            }
        }
        
        // Проверяем id элемента
        const id = element.id;
        if (id && id.startsWith("channel-")) {
            return id.split("-").pop();
        }
        
        return null;
    }
    
    // Получение последнего сообщения для канала
    async getLastMessage(channelId) {
        try {
            if (this.dmChannelsCache.has(channelId)) {
                const cachedData = this.dmChannelsCache.get(channelId);
                if (Date.now() - cachedData.timestamp < 30000 && cachedData.message !== null) {
                    // Возвращаем сам объект сообщения из кэша (может быть не массив)
                    return cachedData.message; 
                }
            }
            
            this.log(`Запрос API для канала ${channelId}`, "info");
            const messages = await this.fetchMessagesFromAPI(channelId);
            
            // Извлекаем само сообщение (первое из массива или null)
            const lastMessage = messages && messages.length > 0 ? messages[0] : null;

            // Кэшируем извлеченное сообщение или null
            this.dmChannelsCache.set(channelId, {
                message: lastMessage, 
                timestamp: Date.now()
            });

            return lastMessage; // Возвращаем сообщение или null
        } catch (error) {
             this.log(`Ошибка getLastMessage для ${channelId}: ${error.message}`, "error");
            this.dmChannelsCache.set(channelId, { message: null, timestamp: Date.now() });
            return null;
        }
    }
    
    // Получение сообщений через API Discord
    async fetchMessagesFromAPI(channelId) {
        try {
            if (this._isUpdating) return null;
            this._isUpdating = true;

            const token = this.getDiscordToken();
            if (!token) {
                 this.log("fetchMessagesFromAPI: Токен не найден", "error"); // Добавляем лог ошибки сюда
                 this._isUpdating = false;
                 return null; // Возвращаем null, если токен не найден
            }

            const headers = {
                'accept': '*/*',
                'accept-language': 'ru,en-US;q=0.9',
                'authorization': token,
                'x-discord-locale': 'ru',
                'x-super-properties': 'eyJvcyI6IldpbmRvd3MiLCJicm93c2VyIjoiRGlzY29yZCBDbGllbnQiLCJyZWxlYXNlX2NoYW5uZWwiOiJwdGIiLCJjbGllbnRfdmVyc2lvbiI6IjEuMC4xMTM3Iiwib3NfdmVyc2lvbiI6IjEwLjAuMTkwNDUiLCJvc19hcmNoIjoieDY0IiwiYXBwX2FyY2giOiJ4NjQiLCJzeXN0ZW1fbG9jYWxlIjoicnUifQ=='
            };

            const response = await fetch(`https://discord.com/api/v9/channels/${channelId}/messages?limit=1`, {
                headers: headers,
                credentials: 'include'
            });

            this._isUpdating = false; // Сбрасываем флаг здесь

            if (!response.ok) {
                 this.log(`Ошибка API ${response.status} для канала ${channelId}`, "error");
                 return null; // Возвращаем null при ошибке API
            }

            const messages = await response.json();
            return messages;

        } catch (error) {
            this._isUpdating = false;
            this.log(`Критическая ошибка fetchMessagesFromAPI для ${channelId}: ${error.message}`, "error"); 
            return null; 
        }
    }
    
    // Обновляем метод получения токена для добавления логгирования
    getDiscordToken() {
        let token = null;
        let method = "";
        this.log("Начинаем поиск токена...", "info"); // Восстановлен info лог
        try {
            // Попытка 1: webpackChunk
            if (typeof window !== 'undefined' && window.webpackChunkdiscord_app) {
                try {
                    const req = window.webpackChunkdiscord_app.push([[Symbol('LMP-TokenFinder')], {}, r => r]);
                    window.webpackChunkdiscord_app.pop();
                    for (const id in req.c) {
                        const m = req.c[id];
                        let potentialTokenFunc = null;
                        let foundFuncName = null;
                        if (m?.exports?.Z?.getToken && typeof m.exports.Z.getToken === 'function') {
                            potentialTokenFunc = m.exports.Z.getToken;
                            foundFuncName = "m.exports.Z.getToken";
                        }
                        if (!token && m?.exports?.default?.getToken && typeof m.exports.default.getToken === 'function') {
                            potentialTokenFunc = m.exports.default.getToken;
                            foundFuncName = "m.exports.default.getToken";
                        }

                        if (potentialTokenFunc) {
                            const result = potentialTokenFunc();
                            // Убраны промежуточные логи о типе и ключах объекта
                            if (typeof result === 'string' && result.length > 50) {
                                token = result;
                                method = `webpackChunk (${foundFuncName})`;
                                this.log(`Токен найден методом ${method}`, "info");
                                break; 
                            } else if (typeof result === 'object' && result !== null) {
                                const commonTokenKeys = ['token', '_token', 'accessToken'];
                                for (const key of commonTokenKeys) {
                                    if (typeof result[key] === 'string' && result[key].length > 50) {
                                        token = result[key];
                                        method = `webpackChunk (${foundFuncName} -> obj.${key})`;
                                        this.log(`Токен найден методом ${method}`, "info");
                                        break; 
                                    }
                                }
                                if (token) break; 
                            }
                        }
                    }
                } catch (e) {
                     this.log(`Ошибка при поиске в webpackChunk: ${e.message}`, "error");
                }
            }

            // Попытка 2: localStorage/cookie
            if (!token) {
                try {
                    const possibleKeys = ['token', 'accessToken', 'auth_token']; 
                    for (const key of possibleKeys) {
                        const value = localStorage.getItem(key);
                        if (value) {
                            token = value.replace(/"/g, '');
                            method = `localStorage ('${key}')`;
                            this.log(`Токен найден методом ${method}`, "info");
                            break;
                        }
                    }
                    if (!token && document.cookie) {
                        const cookieToken = document.cookie.match(/token="(.+?)"/)?.[1];
                        if (cookieToken) {
                            token = cookieToken;
                            method = "cookie";
                            this.log(`Токен найден методом ${method}`, "info");
                        }
                    }
                } catch (e) {
                     this.log(`Ошибка при поиске в localStorage/cookie: ${e.message}`, "error");
                }
            }

            // Попытка 3: UserStore
            if (!token) {
                try {
                    const UserStore = BdApi.Webpack.getStore("UserStore");
                    if (UserStore?.getCurrentUser?.()?.token) { 
                         token = UserStore.getCurrentUser().token;
                         method = "UserStore";
                         this.log(`Токен найден методом ${method}`, "info");
                    }
                } catch (e) {
                    this.log(`Ошибка при поиске в UserStore: ${e.message}`, "error");
                }
            }

            // Финальная проверка
            if (token) {
                if (typeof token !== 'string') {
                     this.log("Полученный токен не является строкой!", "error");
                     return null; 
                }
                return token;
            } else {
                this.log("Токен не найден ни одним из методов.", "error");
                return null; 
            }
        } catch (error) {
             this.log(`Критическая ошибка при получении токена: ${error.message}`, "error");
            return null;
        }
    }
    
    // Создание элемента превью сообщения
    createPreviewElement(message) {
        try {
            let previewContent = "";
            let hasAttachments = false;
            
            if (message.attachments && message.attachments.length > 0) {
                // Убран лог о вложениях
                hasAttachments = this.settings.showImages;
            }
            
            if (message.content) {
                // Убран лог обработки текста
                let content = message.content;
                if (!this.settings.showEmojis) {
                    content = content.replace(/<a?:.+?:\d+>/g, "🙂");
                }
                previewContent = content.length > this.settings.maxPreviewLength ? content.substring(0, this.settings.maxPreviewLength - 3) + "..." : content;
            }
            
            const previewElement = document.createElement("div");
            previewElement.className = "last-message-preview";
            previewElement.style.opacity = this.settings.previewOpacity;
            
            // Добавляем иконку для вложений
            if (hasAttachments) {
                if (previewContent) {
                    previewElement.textContent = `📎 ${previewContent}`;
                } else {
                    previewElement.textContent = "📎 Изображение";
                }
            } else if (!previewContent && message.embeds && message.embeds.length > 0) {
                previewElement.textContent = "📌 Вложение";
            } else if (previewContent) {
                previewElement.textContent = previewContent;
            } else {
                // Нет контента для отображения
                return null;
            }
            
            // Добавляем время, если включено
            if (this.settings.showTimestamp && message.timestamp) {
                const timestamp = new Date(message.timestamp);
                const timeString = timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                
                const timeElement = document.createElement("span");
                timeElement.style.marginLeft = "5px";
                timeElement.style.fontSize = "10px";
                timeElement.textContent = `(${timeString})`;
                
                previewElement.appendChild(timeElement);
            }
            
            // Устанавливаем полный текст как всплывающую подсказку
            previewElement.title = message.content || "Изображение/Вложение";
            
            return previewElement;
        } catch (error) {
            this.log(`Ошибка при создании превью: ${error.message}`, "error"); 
            return null;
        }
    }
    
    // Добавление CSS стилей
    addStyles() {
        this.log("Добавление стилей");
        const { DOM } = BdApi;
        
        DOM.addStyle("LastMessagePreview", this.css);
    }
    
    // Удаление CSS стилей
    removeStyles() {
        const { DOM } = BdApi;
        DOM.removeStyle("LastMessagePreview");
    }

    // Добавляем метод для отладки DOM структуры
    logDOMStructure() {
        this.log("Начало отладки DOM структуры", "info");

        // Проверяем все возможные селекторы для списка ЛС
        this.log("Поиск списка ЛС:", "info");
        for (const selector of this.selectors.dmList) {
            const elements = document.querySelectorAll(selector);
            this.log(`Селектор "${selector}": найдено ${elements.length} элементов`);
        }

        // Проверяем все возможные селекторы для элементов ЛС
        this.log("Поиск элементов ЛС:", "info");
        for (const selector of this.selectors.dmItems) {
            const elements = document.querySelectorAll(selector);
            this.log(`Селектор "${selector}": найдено ${elements.length} элементов`);
            
            if (elements.length > 0) {
                const firstElement = elements[0];
                this.log(`Классы первого элемента: ${firstElement.className}`);
                this.log(`Атрибуты первого элемента: href="${firstElement.getAttribute('href')}", id="${firstElement.id}"`);
            }
        }

        // Начинаем с корня Discord
        const appMount = document.getElementById('app-mount');
        if (!appMount) {
            this.log("Элемент app-mount не найден!", "error");
            return;
        }

        // Логируем основные элементы верхнего уровня
        this.log(`Дочерние элементы app-mount: ${appMount.children.length}`);
        for (let i = 0; i < appMount.children.length; i++) {
            const child = appMount.children[i];
            this.log(`Дочерний ${i}: tag=${child.tagName}, class=${child.className}`);
        }

        // Ищем боковую панель
        const possibleSidebars = [
            ...document.querySelectorAll("nav"),
            ...document.querySelectorAll("[class*='sidebar']"),
            ...document.querySelectorAll("[class*='channels']")
        ];
        this.log(`Найдено ${possibleSidebars.length} возможных боковых панелей`);

        // Сканируем элементы списки возможных элементов чата
        const possibleChatItems = [
            ...document.querySelectorAll("[role='listitem']"),
            ...document.querySelectorAll("a[href*='/channels/@me/']"),
            ...document.querySelectorAll("[class*='channel']")
        ];
        this.log(`Найдено ${possibleChatItems.length} возможных элементов чата`);

        // Выводим пример элемента чата, если он есть
        if (possibleChatItems.length > 0) {
            const sampleItem = possibleChatItems[0];
            this.log("Пример элемента чата:");
            this.log(`Tag: ${sampleItem.tagName}, Classes: ${sampleItem.className}`);
            this.log(`HTML: ${sampleItem.outerHTML.substring(0, 300)}...`);
        }

        this.log("Отладка DOM структуры завершена", "info");
    }

    findDispatcher() {
        this.log("Поиск модуля Dispatcher", "info");
        try {
            this.dispatcher = BdApi.Webpack.getModule(m => m.dispatch && m.subscribe && m.unsubscribe);
            if (this.dispatcher) {
                this.log("Модуль Dispatcher найден успешно", "info");
            } else {
                this.log("Не удалось найти модуль Dispatcher", "error");
            }
        } catch (error) {
            this.log(`Ошибка при поиске Dispatcher: ${error.message}`, "error");
        }
    }

    subscribeToEvents() {
        if (!this.dispatcher) {
            this.log("Невозможно подписаться: Dispatcher не найден", "error");
            return;
        }
        try {
            this.dispatcher.subscribe("MESSAGE_CREATE", this.handleMessageCreate);
            this.log("Успешная подписка на MESSAGE_CREATE", "info");
        } catch (error) {
            this.log(`Ошибка подписки на MESSAGE_CREATE: ${error.message}`, "error");
        }
    }

    unsubscribeFromEvents() {
        if (!this.dispatcher) return; // Нечего отписывать
        try {
            this.dispatcher.unsubscribe("MESSAGE_CREATE", this.handleMessageCreate);
            this.log("Успешная отписка от MESSAGE_CREATE", "info");
        } catch (error) {
            this.log(`Ошибка отписки от MESSAGE_CREATE: ${error.message}`, "error");
        }
    }

    // Обработчик события нового сообщения
    handleMessageCreate(event) {
        // event содержит информацию о событии, включая ID канала и данные сообщения
        if (!event || !event.message || !event.channelId) return;

        const { channelId, message } = event;

        // Проверяем, есть ли этот канал в нашем списке ЛС
        const dmListItem = this.findDmElementByChannelId(channelId);
        if (!dmListItem) {
            // this.log(`Получено сообщение для канала ${channelId}, но элемент не найден в DOM`);
            return; // Канал не отображается в списке ЛС
        }

        this.log(`Получено новое сообщение для канала ${channelId}. Обновление превью...`, "info");
        
        // Обновляем кэш
        this.dmChannelsCache.set(channelId, {
            message: message,
            timestamp: Date.now()
        });

        // Обновляем превью для конкретного элемента
        this.updateSinglePreview(dmListItem, message);
    }
    
    // Вспомогательный метод для поиска элемента ЛС по ID канала
    findDmElementByChannelId(channelId) {
        const links = document.querySelectorAll(`a[href*='/channels/@me/${channelId}']`);
        if (links.length > 0) {
            // Возвращаем родительский li или div, как в processItems
            return links[0].closest('li') || links[0].closest('div[role="listitem"]') || links[0].parentElement;
        }
        return null;
    }
    
    // Метод для обновления одного превью
    updateSinglePreview(dmListItem, message) {
         this.log(`Обновление превью для канала ${message.channel_id}`, "info");
         try {
             let nameWrapper = null;
             // Находим элемент имени внутри dmListItem (родительского элемента)
             for (const selector of this.selectors.nameWrapper) {
                 const wrapper = dmListItem.querySelector(selector);
                 if (wrapper) {
                     nameWrapper = wrapper;
                     break;
                 }
             }
            
             // Резервные варианты поиска nameWrapper (аналогично processItems)
             if (!nameWrapper) {
                 const divs = dmListItem.querySelectorAll("div");
                 for (let i = 0; i < divs.length; i++) {
                     const div = divs[i];
                     if (div.textContent && div.children.length === 0) {
                         nameWrapper = div.parentElement;
                         break;
                     }
                 }
                 if (!nameWrapper && divs.length >= 2) nameWrapper = divs[1];
             }
            
             if (!nameWrapper) nameWrapper = dmListItem; // Крайний случай
             const existingPreview = dmListItem.querySelector(".last-message-preview");
             if (existingPreview) existingPreview.remove();
             const previewElement = this.createPreviewElement(message);
             if (previewElement) {
                 nameWrapper.appendChild(previewElement);
             }
         } catch (error) {
              this.log(`Ошибка при обновлении одиночного превью для ${message.channel_id}: ${error.message}`, "error");
         }
    }
} 