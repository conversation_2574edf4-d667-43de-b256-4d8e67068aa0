/**
 * @name DiscordItalicaHelper
 * @version 0.4.3
 * @description Вспомогательный плагин для расширения Discord Italica (Google Sheets). Использует ZeresPluginLibrary и подключается к внешнему серверу-помощнику.
 * <AUTHOR>
 * @authorId ТвойDiscordID // Добавь свой ID, если хочешь
 * @source https://github.com/ТвойРепозиторий/DiscordItalicaHelper
 * @website https://github.com/ТвойРепозиторий/DiscordItalicaHelper
 * @invite КодПриглашения // Если есть сервер поддержки
 * @updateUrl https://raw.githubusercontent.com/ТвойРепозиторий/DiscordItalicaHelper/main/DiscordItalicaHelper.plugin.js // Ссылка на raw файл плагина для автообновления
 * @donate СсылкаНаДонат // Если хочешь добавить
 */

// Определяем класс в глобальном контексте
const DiscordItalicaHelper = class {
    constructor() {
        // Состояние плагина
        this.helperWs = null;
        this.helperWsUrl = 'ws://127.0.0.1:12346';
        this.reconnectInterval = null;
        this.reconnectTimeout = 5000;
        this.modulesLoaded = false;
        this.libraryInitialized = false;

        // Компоненты ZeresPluginLibrary (будут инициализированы позже)
        this.Library = null;
        this.Logger = null;
        this.Patcher = null;
        this.DiscordModules = null;

        // Основные модули Discord (будут получены в start)
        this.UserStore = null;
        this.ChannelStore = null;
        this.NavigationUtils = null;
        this.RelationshipStore = null;
        // this.SelectedChannelStore = null; // Не используется в коде, можно убрать если не нужен
        // this.Dispatcher = null; // Не используется в коде, можно убрать если не нужен
    }

    // --- Стандартные методы BetterDiscord ---
    getName() { return "DiscordItalicaHelper"; }
    getAuthor() { return "ТвоеИмя"; } // Замените на актуальное
    getDescription() { return "Вспомогательный плагин для расширения Discord Italica (Google Sheets). Использует ZeresPluginLibrary и подключается к внешнему серверу-помощнику."; }
    getVersion() { return "0.4.3"; } // Версия из исходного файла

    // Безопасный логгер, который работает даже без ZeresPluginLibrary
    log(message, type = 'log') {
        const prefix = `[${this.getName()}]`;
        if (this.Logger) {
            switch(type) {
                case 'error': this.Logger.error(message); break;
                case 'warn': this.Logger.warn(message); break;
                case 'info': this.Logger.info(message); break;
                default: this.Logger.log(message);
            }
        } else {
            console[type](`${prefix} ${message}`);
        }
    }

    // Безопасный показ уведомлений
    showToast(content, options = {}) {
        if (BdApi.UI && BdApi.UI.showToast) {
            BdApi.UI.showToast(content, options);
        } else if (BdApi.showToast) {
            BdApi.showToast(content, options);
        } else {
            this.log(content, options.type || 'info');
        }
    }

    // Инициализация ZeresPluginLibrary
    initializeLibrary() {
        if (this.libraryInitialized) return true;

        const ZLib = window.ZLibrary || global.ZeresPluginLibrary;
        if (!ZLib) {
            this.log("ZeresPluginLibrary не найдена. Функциональность будет ограничена.", 'warn');
            return false;
        }

        try {
            this.Library = ZLib.Library || ZLib;
            this.Logger = this.Library.Logger;
            this.Patcher = this.Library.Patcher;
            this.DiscordModules = this.Library.DiscordModules;
            this.libraryInitialized = true;
            this.log("ZeresPluginLibrary успешно инициализирована", 'info');
            return true;
        } catch (e) {
            this.log(`Ошибка инициализации ZeresPluginLibrary: ${e.message}`, 'error');
            return false;
        }
    }

    load() {
        this.log("Загрузка плагина...");
        // Пытаемся инициализировать библиотеку, но не критично если не получилось
        this.initializeLibrary();
        
        // Загружаем сохраненные настройки
        const savedUrl = BdApi.loadData(this.getName(), "helperWsUrl");
        if (savedUrl && savedUrl.startsWith('ws://')) {
            this.helperWsUrl = savedUrl;
            this.log(`Загружена настройка WebSocket URL: ${this.helperWsUrl}`);
        }
    }

    // --- Основная логика плагина ---
    start() {
        this.log("Запуск плагина...");
        
        // Пытаемся инициализировать библиотеку, если ещё не сделали
        if (!this.libraryInitialized && !this.initializeLibrary()) {
            this.showToast(`${this.getName()}: Ограниченная функциональность без ZeresPluginLibrary`, { type: "warning" });
            // Продолжаем работу с ограниченной функциональностью
        }

        // Если библиотека доступна, инициализируем модули Discord
        if (this.DiscordModules) {
            const { UserStore, ChannelStore, NavigationUtils, RelationshipStore } = this.DiscordModules;
            
            if (!UserStore || !ChannelStore || !NavigationUtils || !RelationshipStore) {
                this.log("Не удалось получить критические модули Discord!", 'error');
                this.showToast("Ошибка загрузки модулей Discord!", { type: "error" });
                this.modulesLoaded = false;
            } else {
                this.UserStore = UserStore;
                this.ChannelStore = ChannelStore;
                this.NavigationUtils = NavigationUtils;
                this.RelationshipStore = RelationshipStore;
                this.modulesLoaded = true;
                this.log("Модули Discord успешно получены.");
            }
        }

        // Запускаем WebSocket подключение в любом случае
        this.connectToHelper();
    }

    stop() {
        if (!this.Logger) {
             console.log(`[${this.getName()}] Stopping...`);
        } else {
             this.Logger.log("Stop");
        }

        // Остановка интервала переподключения
        if (this.reconnectInterval) {
            clearInterval(this.reconnectInterval);
            this.reconnectInterval = null;
             if (this.Logger) this.Logger.log("Попытки переподключения остановлены.");
        }

        // Закрытие WebSocket соединения
        if (this.helperWs) {
             if (this.Logger) this.Logger.log("Закрытие соединения с сервером-помощником...");
            this.helperWs.onopen = null;
            this.helperWs.onmessage = null;
            this.helperWs.onerror = null;
            this.helperWs.onclose = null; // Убираем обработчик onclose перед закрытием, чтобы избежать попытки переподключения
            this.helperWs.close();
            this.helperWs = null;
        }

        // Сброс состояния
        this.modulesLoaded = false;
         // Здесь можно добавить Patcher.unpatchAll(), если вы использовали патчинг
         // if (this.Patcher) this.Patcher.unpatchAll(this.getName()); // Пример
    }

    connectToHelper() {
         if (!this.Logger) return; // Не можем работать без логгера (и ZLib)

         if (this.helperWs && (this.helperWs.readyState === WebSocket.OPEN || this.helperWs.readyState === WebSocket.CONNECTING)) {
             this.Logger.log("Соединение уже существует или устанавливается.");
             return;
         }
         // Убедимся, что предыдущий интервал остановлен
         if (this.reconnectInterval) {
             clearInterval(this.reconnectInterval);
             this.reconnectInterval = null;
         }

         this.Logger.log(`Попытка подключения к серверу-помощнику по адресу ${this.helperWsUrl}...`);
         this.showToast("DiscordItalicaHelper: Подключение к помощнику...", { type: "info", timeout: 2000 });

         try {
              if (typeof WebSocket !== 'function') {
                  this.Logger.error("Глобальный конструктор WebSocket недоступен!");
                  this.showToast("DiscordItalicaHelper: Ошибка! WebSocket недоступен.", { type: "error" });
                  this.scheduleReconnect(); // Попробовать переподключиться позже
                  return;
              }
             this.helperWs = new WebSocket(this.helperWsUrl);

             this.helperWs.onopen = () => {
                 this.Logger.log("Успешно подключено к серверу-помощнику.");
                 this.showToast("DiscordItalicaHelper: Подключено", { type: "success" });
                  // Если было активно переподключение, останавливаем его
                  if (this.reconnectInterval) {
                      clearInterval(this.reconnectInterval);
                      this.reconnectInterval = null;
                  }
             };

             this.helperWs.onmessage = (event) => {
                 try {
                     const data = JSON.parse(event.data);
                     this.Logger.log("Получена команда от сервера-помощника:", data);
                     if (data.chatName) {
                         this.handleFindCommand(data.chatName);
                     }
                 } catch (e) {
                     this.Logger.error("Ошибка обработки сообщения от сервера-помощника:", e);
                 }
             };

             // Обработчик onclose теперь отвечает за переподключение
             this.helperWs.onclose = (event) => {
                  const wasConnected = !(event.code === 1006 && event.reason === ''); // Простая проверка, было ли соединение установлено вообще
                  if (wasConnected) {
                      this.Logger.warn(`Соединение с сервером-помощником закрыто. Код: ${event.code}`);
                      this.showToast("DiscordItalicaHelper: Отключено от помощника", { type: "warning" });
                  } else {
                      this.Logger.warn(`Не удалось установить начальное соединение с helper_server.js (Код: ${event.code}). Убедитесь, что он запущен.`);
                  }
                  this.helperWs = null; // Соединение больше неактивно
                  this.scheduleReconnect(); // Запланировать попытку переподключения
             };

             this.helperWs.onerror = (error) => {
                  // Логируем ошибку только если соединение было или пыталось установиться
                  if (this.helperWs && this.helperWs.readyState !== WebSocket.CLOSED) {
                        this.Logger.error("Ошибка WebSocket соединения:", error);
                        this.showToast("DiscordItalicaHelper: Ошибка соединения", { type: "error" });
                  }
                  // onclose будет вызван после onerror, он и запустит переподключение
             };
         } catch (e) {
              this.Logger.error("Не удалось создать WebSocket клиент:", e);
              this.showToast("DiscordItalicaHelper: Критическая ошибка WebSocket.", { type: "error" });
              this.scheduleReconnect(); // Попробовать переподключиться позже
         }
    }

    scheduleReconnect() {
        if (!this.Logger) return; // Нет ZLib - нет переподключения

        // Не планируем переподключение, если интервал уже запущен
        if (this.reconnectInterval) return;

        // Проверяем, активен ли еще плагин (через наличие кнопки в настройках или другим способом)
        // Простой проверки document.getElementById может быть недостаточно, если плагин был выгружен и снова загружен.
        // Лучше положиться на то, что stop() очистит интервал.

        this.Logger.log(`Попытка переподключения через ${this.reconnectTimeout / 1000} сек...`);
        this.reconnectInterval = setInterval(() => {
            // Проверка внутри интервала: если WebSocket уже есть и подключается/открыт, ничего не делаем
            if (this.helperWs && (this.helperWs.readyState === WebSocket.OPEN || this.helperWs.readyState === WebSocket.CONNECTING)) {
                 this.Logger.log("Переподключение отложено, соединение уже активно или устанавливается.");
                 return;
            }
             // Если WebSocket закрыт или его нет, пытаемся подключиться
             this.Logger.log("Запуск попытки переподключения из интервала...");
             // clearInterval(this.reconnectInterval); // Убираем интервал перед новой попыткой
             // this.reconnectInterval = null; // Сбрасываем флаг
             this.connectToHelper(); // connectToHelper сам очистит интервал в случае успеха
        }, this.reconnectTimeout);
    }

    handleFindCommand(chatName) {
         if (!this.Logger || !this.DiscordModules) return;
         try {
            if (!this.modulesLoaded) {
                this.Logger.error("Модули Discord не загружены, обработка команды невозможна.");
                this.showToast("DiscordItalicaHelper: Ошибка: Модули Discord не загружены.", { type: "error" });
                return;
            }

            const foundChannel = this._findChatLogic(chatName);

            if (foundChannel) {
                 this.Logger.log(`Переход в канал ${foundChannel.id}`);
                 this.NavigationUtils.transitionTo(`/channels/@me/${foundChannel.id}`);
                 this.showToast(`Переход в чат: ${foundChannel.name || chatName}`, { type: 'success' });
                 // Активация окна Discord
                 setTimeout(() => window.focus(), 1000);
                 this.Logger.log("делаем окно активным");
            } else {
                this.Logger.log(`Чат с именем "${chatName}" не найден.`);
                this.showToast(`Чат "${chatName}" не найден`, { type: "warning" });
            }
        } catch (error) {
             this.Logger.error("Ошибка при поиске или переходе по команде:", error);
             this.showToast("DiscordItalicaHelper: Внутренняя ошибка", { type: "error" });
        }
    }

    _findChatLogic(chatName) {
         // Используем this.UserStore, this.ChannelStore, this.Logger
         if (!this.UserStore || !this.ChannelStore || !this.Logger) return null;

         const nameLower = chatName.toLowerCase().trim();
         let foundChannel = null;

         // --- Поиск ЛС (DM) ---
         const users = this.UserStore.getUsers();
         for (const userId in users) {
             const user = users[userId];
             if (!user) continue;
             const usernameLower = user.username.toLowerCase();
             const globalNameLower = user.globalName?.toLowerCase(); // Учитываем globalName

             // Собираем возможные варианты имени пользователя для сравнения
             const namesToMatch = [usernameLower];
             if (globalNameLower) namesToMatch.push(globalNameLower);
             // Учитываем старый формат с дискриминатором, если он есть и не '0'
             if (user.discriminator && user.discriminator !== '0') {
                  namesToMatch.push(`${usernameLower}#${user.discriminator}`);
             }

             if (namesToMatch.includes(nameLower)) {
                 const dmChannelId = this.ChannelStore.getDMFromUserId(userId);
                 if (dmChannelId) {
                     foundChannel = this.ChannelStore.getChannel(dmChannelId);
                     if (foundChannel) {
                         this.Logger.log(`Найден ЛС с пользователем ${user.username} (ID: ${userId}), канал ${dmChannelId}`);
                         return foundChannel; // Нашли - выходим
                     } else {
                          this.Logger.warn(`Найден DM ID ${dmChannelId}, но канал не получен из ChannelStore.`);
                          // Продолжаем поиск на случай, если есть другой пользователь с таким же именем (маловероятно, но возможно)
                     }
                 } else {
                      // Пользователь найден, но ЛС с ним нет в кэше ChannelStore
                      this.Logger.log(`Найден пользователь ${user.username}, но ЛС не кэширован.`);
                      // Можно попытаться создать ЛС, но это выходит за рамки простого поиска
                      // NavigationUtils.transitionTo(Routes.DM(userId)); // Это откроет ЛС, но асинхронно
                 }
             }
         }
         this.Logger.log("ЛС по точному совпадению имени пользователя не найден.");

         // --- Поиск Групп (GROUP_DM) ---
         if (!this.ChannelStore.getSortedPrivateChannels) {
              this.Logger.error("ChannelStore.getSortedPrivateChannels отсутствует. Поиск групп невозможен.");
              return null; // Не можем искать группы
         }

         const sortedPrivateChannels = this.ChannelStore.getSortedPrivateChannels();
         const groupChannels = sortedPrivateChannels.filter(ch => ch.type === 3); // 3 = GROUP_DM
         this.Logger.log(`Поиск среди ${groupChannels.length} групповых чатов...`);

         for (const channel of groupChannels) {
             if (!channel || !channel.recipients) continue;

             // 1. По точному имени группы
             if (channel.name && channel.name.toLowerCase() === nameLower) {
                 this.Logger.log(`Найдена группа по точному имени "${channel.name}" (ID: ${channel.id})`);
                 return channel;
             }

             // 2. По именам участников (если имя группы не задано или не совпало)
             // Собираем имена участников, сортируем для консистентности
             const recipientNames = channel.recipients
                 .map(userId => this.UserStore.getUser(userId)?.username) // Получаем username
                 .filter(Boolean) // Убираем null/undefined, если юзер не найден
                 .sort() // Сортируем
                 .join(', ') // Объединяем через ", "
                 .toLowerCase(); // Приводим к нижнему регистру для сравнения

             if (recipientNames === nameLower) {
                 this.Logger.log(`Найдена группа по участникам "${recipientNames}" (ID: ${channel.id})`);
                 return channel;
             }
         }
         this.Logger.log("Группы по точному совпадению имени или участников не найдены.");

         // --- Поиск по частичному совпадению имени группы ---
         this.Logger.log("Поиск группы по частичному совпадению имени...");
         for (const channel of groupChannels) { // Используем тот же список групп
             if (channel && channel.name && channel.name.toLowerCase().includes(nameLower)) {
                 this.Logger.log(`Найдена группа по частичному совпадению имени "${channel.name}" (ID: ${channel.id})`);
                 return channel; // Берем первое совпадение
             }
         }
         this.Logger.log("Группы по частичному совпадению имени не найдены.");


         return null; // Ничего не найдено
    }

    getSettingsPanel() {
        // Проверяем наличие ZeresPluginLibrary
        if (!global.ZeresPluginLibrary) {
            const panel = document.createElement("div");
            panel.style.padding = "20px";
            panel.style.color = "var(--text-normal)";
            panel.innerHTML = `
                <h2>Требуется ZeresPluginLibrary</h2>
                <p>Для отображения настроек требуется <a href="https://betterdiscord.app/plugin/ZeresPluginLibrary" target="_blank" style="color: var(--text-link);">ZeresPluginLibrary</a>.</p>
                <p>Базовые функции плагина могут работать, но настройки недоступны.</p>
            `;
            return panel;
        }

        const { Settings } = ZLibrary;
        const settingItems = [];

        // Основная настройка - адрес WebSocket сервера
        settingItems.push(
            new Settings.Textbox(
                "WebSocket URL",
                "Адрес для подключения к серверу-помощнику (например: ws://127.0.0.1:12346)",
                this.helperWsUrl,
                (newValue) => {
                    if (newValue && newValue.startsWith('ws://')) {
                        this.helperWsUrl = newValue;
                        // Если есть активное соединение, переподключаемся с новым адресом
                        if (this.helperWs) {
                            this.helperWs.close();
                            this.helperWs = null;
                            setTimeout(() => this.connectToHelper(), 500);
                        }
                    } else {
                        this.showToast("Неверный формат URL. Должен начинаться с ws://", { type: "error" });
                    }
                }
            )
        );

        // Кнопка для переподключения
        settingItems.push(
            new Settings.Button(
                "Переподключиться", 
                "Закрыть текущее соединение и подключиться к серверу заново", 
                () => {
                    if (this.helperWs) {
                        this.helperWs.close();
                        this.helperWs = null;
                    }
                    setTimeout(() => this.connectToHelper(), 200);
                    this.showToast("Попытка переподключения...", { type: "info" });
                }
            )
        );

        return Settings.SettingPanel.build(
            () => {
                // Сохраняем настройки при закрытии панели
                BdApi.saveData(this.getName(), "helperWsUrl", this.helperWsUrl);
            },
            ...settingItems
        );
    }
}

// --- ВАЖНО: НЕ УДАЛЯЙТЕ ЭТУ СТРОКУ ---
if (!global.ZeresPluginLibrary) { 
    DiscordItalicaHelper.prototype.load = () => BdApi.alert("Отсутствует ZeresPluginLibrary", "Для работы настроек требуется ZeresPluginLibrary. Некоторые функции могут быть ограничены."); 
}
// --- КОНЕЦ ВАЖНОЙ СТРОКИ ---

// Экспортируем класс для загрузчика
module.exports = DiscordItalicaHelper;
