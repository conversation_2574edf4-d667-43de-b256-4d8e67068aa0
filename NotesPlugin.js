/**
 * @name NotesPlugin
 * @version 0.0.1
 * @description Добавляет раздел заметок в личные сообщения и групповые чаты.
 * <AUTHOR>
 * @source https://github.com/YourRepo/NotesPlugin
 * @website https://betterdiscord.app/
 */

class NotesPlugin {
    constructor() {
        this.notesContainer = null;
        this.textarea = null;
        this.currentChannelId = null;
        this.observer = null; // Для отслеживания изменений в DOM
        this.channelObserver = null; // Для отслеживания смены канала
    }

    // Метод, вызываемый при запуске плагина
    start() {
        console.log("NotesPlugin: Запуск основного плагина...");
        this.initialize();
        // Наблюдатель за сменой канала (более надежный способ появится позже)
        // Пока что будем вызывать loadNotesForChannel принудительно или через DOM observer
        this.observeApp(); // Запускаем наблюдение за DOM для поиска нужного места
    }

    // Метод, вызываемый при остановке плагина
    stop() {
        console.log("NotesPlugin: Остановка основного плагина...");
        this.cleanup();
        if (this.observer) this.observer.disconnect();
        if (this.channelObserver) this.channelObserver.disconnect();
    }

    // Инициализация UI и логики
    initialize() {
        // Логика для поиска целевого элемента и вставки UI заметок
        // Эту часть нужно будет доработать, чтобы она была устойчива к обновлениям Discord
        // Пока используем предоставленный селектор с осторожностью
        const targetSelector = "#app-mount > div.appAsidePanelWrapper_a3002d > div.notAppAsidePanel_a3002d > div.app_a3002d > div > div.layers__960e4.layers__160d8 > div > div > div > div.content_c48ade > div.page_c48ade > div > div > div.content_f75fb0 > div > aside > div";

        this.observer = new MutationObserver(mutations => {
            for (const mutation of mutations) {
                if (mutation.addedNodes.length > 0) {
                     const targetElement = document.querySelector(targetSelector);
                     if (targetElement && !this.notesContainer) {
                         console.log("NotesPlugin: Целевой элемент найден. Создание UI...");
                         this.createNotesUI(targetElement);
                         this.loadNotesForCurrentChannel(); // Загружаем заметки при первой инициализации
                         // Как только UI создан, можно перестать искать элемент (опционально)
                         // this.observer.disconnect();
                     } else if (!targetElement && this.notesContainer) {
                         // Если целевой элемент пропал (например, закрыли ЛС/группу), убираем UI
                         console.log("NotesPlugin: Целевой элемент пропал. Удаление UI...");
                         this.removeNotesUI();
                     }
                }
                // Дополнительно отслеживаем смену канала (упрощенный вариант)
                const newChannelId = this.getCurrentChannelId();
                 if (newChannelId && newChannelId !== this.currentChannelId) {
                    console.log(`NotesPlugin: Канал сменился с ${this.currentChannelId} на ${newChannelId}`);
                    this.currentChannelId = newChannelId;
                    this.loadNotesForCurrentChannel();
                } else if (!newChannelId && this.currentChannelId) {
                    // Если мы больше не в канале (например, на странице друзей)
                    this.currentChannelId = null;
                    if (this.textarea) this.textarea.value = ''; // Очищаем поле
                }
            }
        });

    }

     observeApp() {
        const appMount = document.getElementById('app-mount');
        if (appMount) {
            console.log("NotesPlugin: Запуск MutationObserver для app-mount...");
            this.observer.observe(appMount, { childList: true, subtree: true });
             // Первичная попытка найти элемент сразу
            const targetElement = document.querySelector(this.getTargetSelector());
             if (targetElement && !this.notesContainer) {
                 console.log("NotesPlugin: Целевой элемент найден при запуске. Создание UI...");
                 this.createNotesUI(targetElement);
                 this.loadNotesForCurrentChannel();
             }
        } else {
            console.error("NotesPlugin: Не удалось найти #app-mount!");
        }
    }


    // Получение ID текущего канала (упрощенный вариант)
    // В реальном плагине лучше использовать API BetterDiscord или пропатчить модули Discord
    getCurrentChannelId() {
         // Попробуем найти ID из URL
        const pathParts = window.location.pathname.split('/');
        const channelIdIndex = pathParts.findIndex(part => part === 'channels') + 2; // Ищем ID после /channels/@me/ или /channels/GUILD_ID/
        if (channelIdIndex > 1 && channelIdIndex < pathParts.length && /^\d+$/.test(pathParts[channelIdIndex])) {
             return pathParts[channelIdIndex];
        }
        return null; // Возвращаем null, если ID не найден (например, страница друзей)
    }


    // Создание UI для заметок
    createNotesUI(parentElement) {
        if (this.notesContainer) return; // Уже создано

        this.notesContainer = document.createElement('div');
        this.notesContainer.id = 'notes-plugin-container';
        this.notesContainer.style.padding = '10px';
        this.notesContainer.style.marginTop = '10px';
        this.notesContainer.style.borderTop = '1px solid var(--background-modifier-accent)';

        const title = document.createElement('h3');
        title.textContent = 'Заметки';
        title.style.color = 'var(--header-primary)';
        title.style.marginBottom = '8px';
        title.style.fontSize = '14px'; // Примерный размер

        this.textarea = document.createElement('textarea');
        this.textarea.id = 'notes-plugin-textarea';
        this.textarea.placeholder = 'Введите вашу заметку здесь...';
        this.textarea.style.width = '100%';
        this.textarea.style.minHeight = '100px'; // Начальная высота
        this.textarea.style.resize = 'vertical'; // Разрешить изменять размер по вертикали
        this.textarea.style.backgroundColor = 'var(--background-secondary)';
        this.textarea.style.color = 'var(--text-normal)';
        this.textarea.style.border = '1px solid var(--background-tertiary)';
        this.textarea.style.borderRadius = '3px';
        this.textarea.style.padding = '5px';

        // Обработчик для сохранения при изменении текста (с задержкой)
        let saveTimeout;
        this.textarea.addEventListener('input', () => {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(() => {
                this.saveNotesForCurrentChannel();
            }, 500); // Сохранять через 0.5 секунды после прекращения ввода
        });

        this.notesContainer.appendChild(title);
        this.notesContainer.appendChild(this.textarea);

        // Вставляем контейнер заметок в конец родительского элемента
        parentElement.appendChild(this.notesContainer);
         console.log("NotesPlugin: UI заметок создан и добавлен.");

         // Устанавливаем текущий канал при создании UI
         this.currentChannelId = this.getCurrentChannelId();
    }

    // Удаление UI заметок
    removeNotesUI() {
        if (this.notesContainer) {
            this.notesContainer.remove();
            this.notesContainer = null;
            this.textarea = null; // Сбрасываем ссылку на textarea
             console.log("NotesPlugin: UI заметок удален.");
        }
    }

    // Загрузка заметок для текущего канала
    loadNotesForCurrentChannel() {
        const channelId = this.currentChannelId; // Используем уже установленный ID
        if (!channelId) {
            if (this.textarea) this.textarea.value = ''; // Очистить, если нет канала
            console.log("NotesPlugin: Нет активного канала для загрузки заметок.");
            return;
        }

        if (!this.textarea) {
            console.log("NotesPlugin: Textarea еще не создано, загрузка отложена.");
             // Попытаться найти элемент и создать UI, если его еще нет
             const targetElement = document.querySelector(this.getTargetSelector());
             if (targetElement) {
                 this.createNotesUI(targetElement);
             } else {
                 console.log("NotesPlugin: Целевой элемент не найден, не могу создать UI для загрузки заметок.");
                 return;
             }
        }

        // Используем BdApi для загрузки данных
        const savedNotes = BdApi.Data.load("NotesPlugin", channelId);
        if (this.textarea) { // Убедимся, что textarea существует
            this.textarea.value = savedNotes || ''; // Загружаем заметку или пустую строку
             console.log(`NotesPlugin: Заметки для канала ${channelId} загружены.`);
        }
    }

    // Сохранение заметок для текущего канала
    saveNotesForCurrentChannel() {
        const channelId = this.currentChannelId;
        if (!channelId || !this.textarea) {
             console.log("NotesPlugin: Нет активного канала или textarea для сохранения заметок.");
            return; // Не сохранять, если нет канала или textarea
        }

        const notesToSave = this.textarea.value;
        // Используем BdApi для сохранения данных
        BdApi.Data.save("NotesPlugin", channelId, notesToSave);
        console.log(`NotesPlugin: Заметки для канала ${channelId} сохранены.`);
    }

    // Очистка ресурсов при остановке плагина
    cleanup() {
        this.removeNotesUI();
        // Дополнительная очистка, если требуется (например, удаление слушателей)
    }

     // Вспомогательная функция для получения селектора
     getTargetSelector() {
        // Используем селектор, предоставленный пользователем, но его нужно будет улучшить
        return "#app-mount > div.appAsidePanelWrapper_a3002d > div.notAppAsidePanel_a3002d > div.app_a3002d > div > div.layers__960e4.layers__160d8 > div > div > div > div.content_c48ade > div.page_c48ade > div > div > div.content_f75fb0 > div > aside > div";
    }

};
